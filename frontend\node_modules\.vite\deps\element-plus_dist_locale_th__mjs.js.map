{"version": 3, "sources": ["../../.pnpm/element-plus@2.10.1_vue@3.5.16/node_modules/element-plus/dist/locale/th.mjs"], "sourcesContent": ["/*! Element Plus v2.10.1 */\n\nvar th = {\n  name: \"th\",\n  el: {\n    breadcrumb: {\n      label: \"Breadcrumb\"\n    },\n    colorpicker: {\n      confirm: \"\\u0E15\\u0E01\\u0E25\\u0E07\",\n      clear: \"\\u0E25\\u0E49\\u0E32\\u0E07\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\"\n    },\n    datepicker: {\n      now: \"\\u0E15\\u0E2D\\u0E19\\u0E19\\u0E35\\u0E49\",\n      today: \"\\u0E27\\u0E31\\u0E19\\u0E19\\u0E35\\u0E49\",\n      cancel: \"\\u0E22\\u0E01\\u0E40\\u0E25\\u0E34\\u0E01\",\n      clear: \"\\u0E25\\u0E49\\u0E32\\u0E07\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\",\n      confirm: \"\\u0E15\\u0E01\\u0E25\\u0E07\",\n      selectDate: \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\",\n      selectTime: \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E40\\u0E27\\u0E25\\u0E32\",\n      startDate: \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\\u0E40\\u0E23\\u0E34\\u0E48\\u0E21\\u0E15\\u0E49\\u0E19\",\n      startTime: \"\\u0E40\\u0E27\\u0E25\\u0E32\\u0E40\\u0E23\\u0E34\\u0E48\\u0E21\\u0E15\\u0E49\\u0E19\",\n      endDate: \"\\u0E27\\u0E31\\u0E19\\u0E17\\u0E35\\u0E48\\u0E2A\\u0E34\\u0E49\\u0E19\\u0E2A\\u0E38\\u0E14\",\n      endTime: \"\\u0E40\\u0E27\\u0E25\\u0E32\\u0E2A\\u0E34\\u0E49\\u0E19\\u0E2A\\u0E38\\u0E14\",\n      prevYear: \"\\u0E1B\\u0E35\\u0E01\\u0E48\\u0E2D\\u0E19\\u0E2B\\u0E19\\u0E49\\u0E32\",\n      nextYear: \"\\u0E1B\\u0E35\\u0E16\\u0E31\\u0E14\\u0E44\\u0E1B\",\n      prevMonth: \"\\u0E40\\u0E14\\u0E37\\u0E2D\\u0E19\\u0E01\\u0E48\\u0E2D\\u0E19\\u0E2B\\u0E19\\u0E49\\u0E32\",\n      nextMonth: \"\\u0E40\\u0E14\\u0E37\\u0E2D\\u0E19\\u0E16\\u0E31\\u0E14\\u0E44\\u0E1B\",\n      year: \"\\u0E1B\\u0E35\",\n      month1: \"\\u0E21\\u0E01\\u0E23\\u0E32\\u0E04\\u0E21\",\n      month2: \"\\u0E01\\u0E38\\u0E21\\u0E20\\u0E32\\u0E1E\\u0E31\\u0E19\\u0E18\\u0E4C\",\n      month3: \"\\u0E21\\u0E35\\u0E19\\u0E32\\u0E04\\u0E21\",\n      month4: \"\\u0E40\\u0E21\\u0E29\\u0E32\\u0E22\\u0E19\",\n      month5: \"\\u0E1E\\u0E24\\u0E29\\u0E20\\u0E32\\u0E04\\u0E21\",\n      month6: \"\\u0E21\\u0E34\\u0E16\\u0E38\\u0E19\\u0E32\\u0E22\\u0E19\",\n      month7: \"\\u0E01\\u0E23\\u0E01\\u0E0E\\u0E32\\u0E04\\u0E21\",\n      month8: \"\\u0E2A\\u0E34\\u0E07\\u0E2B\\u0E32\\u0E04\\u0E21\",\n      month9: \"\\u0E01\\u0E31\\u0E19\\u0E22\\u0E32\\u0E22\\u0E19\",\n      month10: \"\\u0E15\\u0E38\\u0E25\\u0E32\\u0E04\\u0E21\",\n      month11: \"\\u0E1E\\u0E24\\u0E28\\u0E08\\u0E34\\u0E01\\u0E32\\u0E22\\u0E19\",\n      month12: \"\\u0E18\\u0E31\\u0E19\\u0E27\\u0E32\\u0E04\\u0E21\",\n      weeks: {\n        sun: \"\\u0E2D\\u0E32\",\n        mon: \"\\u0E08\",\n        tue: \"\\u0E2D\",\n        wed: \"\\u0E1E\",\n        thu: \"\\u0E1E\\u0E24\",\n        fri: \"\\u0E28\",\n        sat: \"\\u0E2A\"\n      },\n      months: {\n        jan: \"\\u0E21.\\u0E04.\",\n        feb: \"\\u0E01.\\u0E1E.\",\n        mar: \"\\u0E21\\u0E35.\\u0E04.\",\n        apr: \"\\u0E40\\u0E21.\\u0E22.\",\n        may: \"\\u0E1E.\\u0E04.\",\n        jun: \"\\u0E21\\u0E34.\\u0E22.\",\n        jul: \"\\u0E01.\\u0E04.\",\n        aug: \"\\u0E2A.\\u0E04.\",\n        sep: \"\\u0E01.\\u0E22.\",\n        oct: \"\\u0E15.\\u0E04.\",\n        nov: \"\\u0E1E.\\u0E22.\",\n        dec: \"\\u0E18.\\u0E04.\"\n      }\n    },\n    select: {\n      loading: \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14\",\n      noMatch: \"\\u0E44\\u0E21\\u0E48\\u0E1E\\u0E1A\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\\u0E17\\u0E35\\u0E48\\u0E15\\u0E23\\u0E07\\u0E01\\u0E31\\u0E19\",\n      noData: \"\\u0E44\\u0E21\\u0E48\\u0E1E\\u0E1A\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\",\n      placeholder: \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\"\n    },\n    mention: {\n      loading: \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14\"\n    },\n    cascader: {\n      noMatch: \"\\u0E44\\u0E21\\u0E48\\u0E1E\\u0E1A\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\\u0E17\\u0E35\\u0E48\\u0E15\\u0E23\\u0E07\\u0E01\\u0E31\\u0E19\",\n      loading: \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14\",\n      placeholder: \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\",\n      noData: \"\\u0E44\\u0E21\\u0E48\\u0E1E\\u0E1A\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\"\n    },\n    pagination: {\n      goto: \"\\u0E44\\u0E1B\\u0E17\\u0E35\\u0E48\",\n      pagesize: \"/\\u0E2B\\u0E19\\u0E49\\u0E32\",\n      total: \"\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14 {total}\",\n      pageClassifier: \"\",\n      page: \"Page\",\n      prev: \"Go to previous page\",\n      next: \"Go to next page\",\n      currentPage: \"page {pager}\",\n      prevPages: \"Previous {pager} pages\",\n      nextPages: \"Next {pager} pages\"\n    },\n    messagebox: {\n      title: \"\\u0E02\\u0E49\\u0E2D\\u0E04\\u0E27\\u0E32\\u0E21\",\n      confirm: \"\\u0E15\\u0E01\\u0E25\\u0E07\",\n      cancel: \"\\u0E22\\u0E01\\u0E40\\u0E25\\u0E34\\u0E01\",\n      error: \"\\u0E04\\u0E38\\u0E13\\u0E1B\\u0E49\\u0E2D\\u0E19\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\\u0E44\\u0E21\\u0E48\\u0E16\\u0E39\\u0E01\\u0E15\\u0E49\\u0E2D\\u0E07\"\n    },\n    upload: {\n      deleteTip: '\\u0E01\\u0E14\\u0E1B\\u0E38\\u0E48\\u0E21 \"\\u0E25\\u0E1A\" \\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E1A\\u0E2D\\u0E2D\\u0E01',\n      delete: \"\\u0E25\\u0E1A\",\n      preview: \"\\u0E15\\u0E31\\u0E27\\u0E2D\\u0E22\\u0E48\\u0E32\\u0E07\",\n      continue: \"\\u0E17\\u0E33\\u0E15\\u0E48\\u0E2D\"\n    },\n    table: {\n      emptyText: \"\\u0E44\\u0E21\\u0E48\\u0E1E\\u0E1A\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\",\n      confirmFilter: \"\\u0E22\\u0E37\\u0E19\\u0E22\\u0E31\\u0E19\",\n      resetFilter: \"\\u0E23\\u0E35\\u0E40\\u0E0B\\u0E47\\u0E15\",\n      clearFilter: \"\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\",\n      sumText: \"\\u0E23\\u0E27\\u0E21\"\n    },\n    tour: {\n      next: \"\\u0E16\\u0E31\\u0E14\\u0E44\\u0E1B\",\n      previous: \"\\u0E22\\u0E49\\u0E2D\\u0E19\\u0E01\\u0E25\\u0E31\\u0E1A\",\n      finish: \"\\u0E40\\u0E2A\\u0E23\\u0E47\\u0E08\\u0E2A\\u0E34\\u0E49\\u0E19\"\n    },\n    tree: {\n      emptyText: \"\\u0E44\\u0E21\\u0E48\\u0E1E\\u0E1A\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\"\n    },\n    transfer: {\n      noMatch: \"\\u0E44\\u0E21\\u0E48\\u0E1E\\u0E1A\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\\u0E17\\u0E35\\u0E48\\u0E15\\u0E23\\u0E07\\u0E01\\u0E31\\u0E19\",\n      noData: \"\\u0E44\\u0E21\\u0E48\\u0E1E\\u0E1A\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\",\n      titles: [\"List 1\", \"List 2\"],\n      filterPlaceholder: \"\\u0E01\\u0E23\\u0E2D\\u0E01\\u0E04\\u0E35\\u0E22\\u0E4C\\u0E40\\u0E27\\u0E34\\u0E23\\u0E4C\\u0E14\",\n      noCheckedFormat: \"{total} items\",\n      hasCheckedFormat: \"{checked}/{total} checked\"\n    },\n    image: {\n      error: \"FAILED\"\n    },\n    pageHeader: {\n      title: \"\\u0E22\\u0E49\\u0E2D\\u0E19\\u0E01\\u0E25\\u0E31\\u0E1A\"\n    },\n    popconfirm: {\n      confirmButtonText: \"Yes\",\n      cancelButtonText: \"No\"\n    },\n    carousel: {\n      leftArrow: \"Carousel arrow left\",\n      rightArrow: \"Carousel arrow right\",\n      indicator: \"Carousel switch to index {index}\"\n    }\n  }\n};\n\nexport { th as default };\n"], "mappings": ";;;AAEA,IAAI,KAAK;AAAA,EACP,MAAM;AAAA,EACN,IAAI;AAAA,IACF,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA,MACX,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,MACA,QAAQ;AAAA,QACN,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,aAAa;AAAA,IACf;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,aAAa;AAAA,MACb,WAAW;AAAA,MACX,WAAW;AAAA,IACb;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,WAAW;AAAA,MACX,eAAe;AAAA,MACf,aAAa;AAAA,MACb,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,MAAM;AAAA,MACJ,WAAW;AAAA,IACb;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ,CAAC,UAAU,QAAQ;AAAA,MAC3B,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,IACpB;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,kBAAkB;AAAA,IACpB;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,EACF;AACF;", "names": []}