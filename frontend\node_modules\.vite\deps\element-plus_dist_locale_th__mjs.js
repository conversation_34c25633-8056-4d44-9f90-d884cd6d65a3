import "./chunk-G3PMV62Z.js";

// node_modules/.pnpm/element-plus@2.10.1_vue@3.5.16/node_modules/element-plus/dist/locale/th.mjs
var th = {
  name: "th",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "ตกลง",
      clear: "ล้างข้อมูล"
    },
    datepicker: {
      now: "ตอนนี้",
      today: "วันนี้",
      cancel: "ยกเลิก",
      clear: "ล้างข้อมูล",
      confirm: "ตกลง",
      selectDate: "เลือกวันที่",
      selectTime: "เลือกเวลา",
      startDate: "วันที่เริ่มต้น",
      startTime: "เวลาเริ่มต้น",
      endDate: "วันที่สิ้นสุด",
      endTime: "เวลาสิ้นสุด",
      prevYear: "ปีก่อนหน้า",
      nextYear: "ปีถัดไป",
      prevMonth: "เดือนก่อนหน้า",
      nextMonth: "เดือนถัดไป",
      year: "ปี",
      month1: "มกราคม",
      month2: "กุมภาพันธ์",
      month3: "มีนาคม",
      month4: "เมษายน",
      month5: "พฤษภาคม",
      month6: "มิถุนายน",
      month7: "กรกฎาคม",
      month8: "สิงหาคม",
      month9: "กันยายน",
      month10: "ตุลาคม",
      month11: "พฤศจิกายน",
      month12: "ธันวาคม",
      weeks: {
        sun: "อา",
        mon: "จ",
        tue: "อ",
        wed: "พ",
        thu: "พฤ",
        fri: "ศ",
        sat: "ส"
      },
      months: {
        jan: "ม.ค.",
        feb: "ก.พ.",
        mar: "มี.ค.",
        apr: "เม.ย.",
        may: "พ.ค.",
        jun: "มิ.ย.",
        jul: "ก.ค.",
        aug: "ส.ค.",
        sep: "ก.ย.",
        oct: "ต.ค.",
        nov: "พ.ย.",
        dec: "ธ.ค."
      }
    },
    select: {
      loading: "กำลังโหลด",
      noMatch: "ไม่พบข้อมูลที่ตรงกัน",
      noData: "ไม่พบข้อมูล",
      placeholder: "เลือก"
    },
    mention: {
      loading: "กำลังโหลด"
    },
    cascader: {
      noMatch: "ไม่พบข้อมูลที่ตรงกัน",
      loading: "กำลังโหลด",
      placeholder: "เลือก",
      noData: "ไม่พบข้อมูล"
    },
    pagination: {
      goto: "ไปที่",
      pagesize: "/หน้า",
      total: "ทั้งหมด {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "ข้อความ",
      confirm: "ตกลง",
      cancel: "ยกเลิก",
      error: "คุณป้อนข้อมูลไม่ถูกต้อง"
    },
    upload: {
      deleteTip: 'กดปุ่ม "ลบ" เพื่อลบออก',
      delete: "ลบ",
      preview: "ตัวอย่าง",
      continue: "ทำต่อ"
    },
    table: {
      emptyText: "ไม่พบข้อมูล",
      confirmFilter: "ยืนยัน",
      resetFilter: "รีเซ็ต",
      clearFilter: "ทั้งหมด",
      sumText: "รวม"
    },
    tour: {
      next: "ถัดไป",
      previous: "ย้อนกลับ",
      finish: "เสร็จสิ้น"
    },
    tree: {
      emptyText: "ไม่พบข้อมูล"
    },
    transfer: {
      noMatch: "ไม่พบข้อมูลที่ตรงกัน",
      noData: "ไม่พบข้อมูล",
      titles: ["List 1", "List 2"],
      filterPlaceholder: "กรอกคีย์เวิร์ด",
      noCheckedFormat: "{total} items",
      hasCheckedFormat: "{checked}/{total} checked"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "ย้อนกลับ"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};
export {
  th as default
};
/*! Bundled license information:

element-plus/dist/locale/th.mjs:
  (*! Element Plus v2.10.1 *)
*/
//# sourceMappingURL=element-plus_dist_locale_th__mjs.js.map
